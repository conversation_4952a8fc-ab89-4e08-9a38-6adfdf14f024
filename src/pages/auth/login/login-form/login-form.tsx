/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-useless-escape */
import { FC, useCallback, useMemo, useState } from 'react'
import { LoginFormProps } from './login-form.d'
import classNamesBind from 'classnames/bind'
import styles from './login-form.module.scss'
import { FormTitle, Input, Button } from '@/shared/ui'
import { SubmitHandler, useForm } from 'react-hook-form'
import { authAPI } from 'entities/auth'
import { useAppDispatch, useAppSelector } from '@/store'
import { Link, useNavigate } from 'react-router-dom'
import { URLS } from '@/shared/configs/urls'
import { TwoFaAuth } from '@/shared/components'
import { TwoFAVerifyRequest } from '@/shared/types/store/twofa'
import { useNotification } from '@/shared/contexts/notifications'
import { userAPI } from 'entities/employee'
import { logIn, selectAuthSteps, setAuthSteps, setTwofaAssignCode } from '@/store/slices/auth'
import { LoginTwofaVerify } from './login-twofa-verify'
import { useTranslation } from 'react-i18next'
import { useSAML } from '../use-saml'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import { AUTH_ERROR_MESSAGES } from '@/shared/constants'

const cx = classNamesBind.bind(styles)
const { useLoginMutation, useTwofaCodeVerifyMutation } = authAPI

interface ILoginInputs {
  email: string
  password: string
}

export const LoginForm: FC<LoginFormProps.Props> = props => {
  const { className } = props

  const { t } = useTranslation()

  const [login, { isLoading }] = useLoginMutation()
  const navigate = useNavigate()
  const [userTrigger] = userAPI.endpoints.getUserInfo.useLazyQuery()
  const [error, setError] = useState<string | null>(null)
  const dispatch = useAppDispatch()

  const { SamlLogin } = useSAML()
  const [twofaVerifyTrigger, { isLoading: twofaVerifyLoading, isError: twofaError }] =
    useTwofaCodeVerifyMutation()

  const { handleResponse } = useNotification()

  const AGREEMENT_ERROR_MESSAGE = t('commons:agreement_error_message')

  const onSuccessLogin = useCallback(() => {
    dispatch(logIn())

    const refecthPermissions = async () => {
      await userTrigger().unwrap()
      navigate(URLS.USER_MY_COURSES_PAGE)
    }

    refecthPermissions()

    setError('')
    reset()
    handleResponse(t('commons:success_enter'))
  }, [userTrigger, t])

  const twofaVerify = useCallback(
    async (values: TwoFAVerifyRequest) => {
      await twofaVerifyTrigger(values)
        .unwrap()
        .then(() => {
          onSuccessLogin()
          dispatch(setAuthSteps({ twofa: false }))
        })
    },
    [twofaVerifyTrigger, onSuccessLogin, dispatch],
  )
  const steps = useAppSelector(selectAuthSteps)

  const resolver = useMemo(
    () =>
      zodResolver(
        z.object({
          email: z
            .string()
            .min(1, {
              message: t('commons:required_field'),
            })
            .email({ message: t('commons:email_example') }),
          password: z
            .string()
            .min(1, {
              message: t('commons:required_field'),
            })
            .refine(val => !!val.trim()),
        }),
      ),
    [t],
  )

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setError: setErrorForm,
    setValue,
    getValues,
  } = useForm<ILoginInputs>({
    mode: 'onBlur',
    defaultValues: {
      email: '',
      password: '',
    },
    resolver,
  })

  const handleTrim = (str: string, name: keyof ILoginInputs, isRequired = false) => {
    if (!str.trim().length && isRequired) {
      setErrorForm(name, { type: 'custom', message: t('commons:required_field') })
    }
    setValue(name, str.trim())
  }

  const onSubmit: SubmitHandler<ILoginInputs> = data => {
    login(data)
      .unwrap()
      .then(() => {
        userTrigger()
          .unwrap()
          .then(() => {
            // permissionsTrigger().then(() => {
            dispatch(logIn())
            navigate(URLS.USER_MY_COURSES_PAGE)
            // })
          })
          .catch((e: any) => {
            if (e?.data?.message === AGREEMENT_ERROR_MESSAGE) {
              dispatch(setAuthSteps({ permissions: true }))
            }
          })

        setError('')
        reset()
      })
      .catch(error => {
        //двухфакторка
        if (error.status === 403 && AUTH_ERROR_MESSAGES.includes(error?.data?.detail)) {
          const data = error?.data?.data
          if (data?.value) {
            dispatch(setTwofaAssignCode(data?.value))
            dispatch(setAuthSteps({ twofa__assign: true }))
          } else {
            dispatch(setAuthSteps({ twofa: true }))
          }
        }
        //политика данных
        if (error?.data?.message === AGREEMENT_ERROR_MESSAGE) {
          dispatch(setAuthSteps({ permissions: true }))
        }
        setError(error?.data?.message)
      })
  }

  return (
    <>
      {!steps['twofa'] && !steps['twofa__assign'] && (
        <form onSubmit={handleSubmit(onSubmit)} className={cx(className, 'wrapper')}>
          <FormTitle>{t('commons:login_to_personal_account')}</FormTitle>
          <Input
            // eslint-disable-next-line i18next/no-literal-string
            label='Email'
            type='email'
            placeholder={t('commons:enter_email')}
            classNameWrapper={cx('input')}
            error={errors.email?.message}
            fullWidth
            register={register('email', {
              onBlur: e => handleTrim(e.target.value, 'email', true),
            })}
          />
          <Input
            label={t('commons:password')}
            type='password'
            placeholder={t('commons:enter_password')}
            classNameWrapper={cx('input', 'double')}
            error={errors.password?.message}
            fullWidth
            register={register('password', {
              onBlur: e => handleTrim(e.target.value, 'password', true),
            })}
          />
          <Button
            fullWidth
            type='submit'
            color='green'
            size='big'
            loading={isLoading}
            disabled={!isValid}
          >
            {t('commons:come_in')}
          </Button>
          {error && <div className={cx('error-text', 'errorText')}>{error}</div>}
          <Link to='/lk/auth/password/recovery' className={cx('restorePassword')}>
            {t('commons:forgot_password')}
          </Link>
          {/* eslint-disable-next-line i18next/no-literal-string */}
          <div className={cx('adfs-button')} onClick={SamlLogin}>
            SAML SSO
          </div>
        </form>
      )}
      {steps['twofa'] && (
        <div className={cx(styles.twofa)}>
          <FormTitle>{t('commons:two_factor_authentication')}</FormTitle>
          <TwoFaAuth
            isError={twofaError}
            inputProps={{ disabled: twofaVerifyLoading }}
            className={cx(styles.twofa__input)}
            codeLength={6}
            onSuccess={v => {
              twofaVerify({ code: v, email: getValues()?.email })
            }}
          />
        </div>
      )}
      {steps['twofa__assign'] && <LoginTwofaVerify email={getValues()?.email} />}
    </>
  )
}
